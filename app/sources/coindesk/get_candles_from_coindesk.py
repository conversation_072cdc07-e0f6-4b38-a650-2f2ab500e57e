import pandas as pd

from datetime import datetime
from app.logger.get_logger import log, logger
from app.sources.coindesk.minute import get_historical_minutes
from app.sources.coindesk.hourly import get_historical_hours
from app.sources.coindesk.daily import get_historical_days

"""
Coindesk API Client
Documentation: https://developers.coindesk.com/documentation/data-api

Available data:
- Crypto prices and market data
- OHLCV data (minute to daily)
- Market aggregates
- Exchange data
- News and insights
- Historical data

Pricing (as of 2024):
- Free tier: Basic access with rate limits
- Professional: Custom pricing based on usage
- Enterprise: Custom solutions and dedicated support

API key required for most endpoints
Visit https://data.coindesk.com for current pricing and features
"""

# Coindesk currency pair mapping (new btc-usd format to Coindesk format)
COINDESK_PAIRS = {
        'btc-usd': 'BTC-USD',  # Coindesk uses BTC-USD format
        # Note: Coindesk primarily supports BTC/USD only
}


def validate_coindesk_pair(currency_pair: str) -> tuple[bool, str]:
    """
    Validate currency pair format and check Coindesk support.

    Args:
        currency_pair: Format like 'btc-usd'

    Returns:
        Tuple of (is_valid, error_message)
    """
    if '-' not in currency_pair:
        return False, "Invalid format. Use 'base-quote' format (e.g., 'btc-usd')"

    if currency_pair not in COINDESK_PAIRS:
        supported = list(COINDESK_PAIRS.keys())
        return False, f"Currency pair {currency_pair} not supported by Coindesk. Supported: {supported}"

    return True, ""


@log
def get_candles_from_coindesk(currency_pair: str, timeframe: str, candles: int,
                              ecc: bool = True) -> pd.DataFrame:
    """
    Main function to retrieve OHLC data from Coindesk API.
    Makes multiple calls to aggregate the data with appropriate limits based on timeframe.

    Parameters
    ----------
    currency_pair : str
        The trading pair in new format (e.g., 'btc-usd')
    timeframe : str
        The timeframe for data points ('m1', 'm15' for minutes, 'h1', 'h4' for hours, 'd1' for daily)
    candles : int
        Number of candles to retrieve
    ecc : bool, optional
        Exclude current candle flag (default: True)

    Returns
    -------
    pd.DataFrame
        df with columns: timestamp, date, open, high, low, close, volume
    """
    # Map timeframes to functions and aggregation values
    timeframe_functions = {
            "m1" : (get_historical_minutes, 1, 2000),
            "m5" : (get_historical_minutes, 5, 400),
            "m15": (get_historical_minutes, 15, 133),
            "m30": (get_historical_minutes, 30, 66),
            "h1" : (get_historical_hours, 1, 2000),
            "h4" : (get_historical_hours, 4, 500),
            "d1" : (get_historical_days, 1, 2000)
    }

    if timeframe not in timeframe_functions:
        raise ValueError(f"Invalid timeframe. Must be one of: {', '.join(timeframe_functions.keys())}")

    # Validate currency pair format and Coindesk support
    is_valid, error_msg = validate_coindesk_pair(currency_pair)
    if not is_valid:
        logger.error(f"Coindesk validation failed: {error_msg}")
        return pd.DataFrame()

    # Convert to Coindesk format
    instrument = COINDESK_PAIRS[currency_pair]
    logger.info(f"Fetching {candles} {timeframe} candles for {currency_pair} ({instrument}) from Coindesk")

    master_data = []
    current_timestamp = int(datetime.now().timestamp())

    func, aggregation, limit = timeframe_functions[timeframe]

    # Coindesk API has a limit of 2000 candles per request for m1, h1, d1
    max_limit_per_request = limit
    returned_candles = 0

    # If ecc is True, we need to request one additional candle to compensate
    # for the one that will be removed, but don't exceed the max limit
    target_candles = candles
    if ecc and candles < max_limit_per_request:
        target_candles += 1

    while target_candles > returned_candles:
        # Make the API call
        df = func(
                instrument = instrument,
                aggregate = aggregation,
                limit = max_limit_per_request,
                to_ts = current_timestamp
        )

        if df.empty:
            break

        # Update the timestamp for the next request
        current_timestamp = df['timestamp'].min()
        returned_candles += len(df)
        master_data.append(df)

    if not master_data:
        return pd.DataFrame()

    # Combine all the data
    df = pd.concat(master_data, ignore_index = True)
    df = df[["timestamp", "date", "open", "high", "low", "close", "volume"]]
    df = df.sort_values(by = "timestamp", ascending = False)
    df = df.drop_duplicates(subset = "timestamp", keep = "first")

    # Drop the newest candle if ecc is True
    if ecc and not df.empty:
        df = df.iloc[1:]

    return df


def get_supported_coindesk_pairs() -> list:
    """Get list of supported currency pairs for Coindesk."""
    return list(COINDESK_PAIRS.keys())
