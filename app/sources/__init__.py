# 1. all sources should have the same structure:
# - __init__.py
# - get_candles_from_{source}.py
# - get_range_from_{source}.py
# - api (if needed)
# - helpers (if needed)
# - tests (if needed)
#
# 2. All get_candles_from_{source}.py should take the same parameters and return the same format. All get_range_from_{source}.py should take the same parameters and return the same format.
# 3. All get_candles_from_{source}.py should be compatible with /app/core/refresh_source_db_by_candles.py. All get_range_from_{source}.py should be compatible with /app/core/refresh_source_db_by_range.py

