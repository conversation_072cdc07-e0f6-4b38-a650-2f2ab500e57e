import pandas as pd
import pytz
from datetime import datetime
from fastapi import APIRouter
from typing import List, Tuple, Optional

from app.api.endpoints.get.helpers.verify_data_freshness import verify_data_freshness
from app.core.refresh_source_db_by_candles import refresh_source_db_by_candles
from app.api.endpoints.get.helpers.get_missing_timestamps import get_missing_timestamps
from app.api.endpoints.get.helpers.is_data_complete import is_data_complete
from app.db.sqlite.get_from_db import get_from_db
from app.logger.get_logger import log, logger

router = APIRouter()


@router.get("/{currency_pair}/{timeframe}/{candles}")
@log
def get_candles_endpoint(currency_pair: str, timeframe: str, candles: str):
    """Get OHLC candles for a given currency pair and timeframe."""
    api_call_timestamp = int(datetime.now(pytz.utc).timestamp())

    # Try getting data from internal DB first
    data, errors1 = get_validated_data(currency_pair, timeframe, candles, api_call_timestamp)
    if data:
        return data

    # If data invalid/missing, refresh DB and try again
    refresh_source_db_by_candles(currency_pair, timeframe, int(candles))
    data, errors2 = get_validated_data(currency_pair, timeframe, candles, api_call_timestamp)
    if data:
        return data

    logger.error("------------------------------------------------------------")
    return {"error"  : "dabot.ohlc Failed", "errors1": errors1, "errors2": errors2}


def get_validated_data(currency_pair: str, timeframe: str, candles: str,
                       api_call_timestamp: int) -> tuple[list[dict], None] | tuple[None, list[str]]:
    """Get and validate data from database."""
    db_data = check_db(currency_pair, timeframe, candles)
    is_valid, errors = _validate_data(db_data, timeframe, candles, api_call_timestamp)

    if is_valid:
        return db_data.sort_values(by = 'timestamp', ascending = False).to_dict(orient = 'records'), None
    return None, errors


def check_db(currency_pair: str, timeframe: str, candles: str) -> Optional[pd.DataFrame]:
    """Safely retrieve data from database."""
    try:
        return get_from_db(currency_pair, timeframe, candles, "bitstamp")
    except (ValueError, Exception):
        return None


def _validate_data(df: Optional[pd.DataFrame], timeframe: str, candles: str,
                   api_call_timestamp: int) -> Tuple[bool, List[str]]:
    """Validate DataFrame against multiple criteria."""
    errors = []

    if df is None:
        return False, ["DataFrame is None"]
    if not isinstance(df, pd.DataFrame):
        return False, ["Input is not a pandas DataFrame"]
    if df.empty:
        errors.append("DataFrame is empty")
    if not is_data_complete(df, candles):
        errors.append("Wrong number of candles")
    if not verify_data_freshness(df, timeframe, api_call_timestamp):
        errors.append("Data is not fresh")
    missing_timestamps = get_missing_timestamps(df, timeframe, None, None)
    if missing_timestamps:
        errors.append(f"Missing timestamps: {missing_timestamps}")
    if errors:
        logger.warn(f"Validation failed: {errors}")
        return False, errors

    return True, []
