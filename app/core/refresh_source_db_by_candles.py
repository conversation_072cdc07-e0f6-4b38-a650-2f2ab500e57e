from app.db.sqlite.store_in_db import store_in_db
from app.core.discover_source_functions import discover_source_candles_functions
from app.logger.get_logger import log, logger

@log
def refresh_source_db_by_candles(currency_pair: str, timeframe: str, candles: int, ecc: bool = True) -> None:
    """
    Fetches candle data from multiple external sources and stores it in the database.

    Automatically discovers available source functions instead of using hardcoded sources.

    Args:
        currency_pair: Trading pair (e.g., 'btc-usd')
        timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
        candles: Number of candles to fetch
        ecc: Exclude current candle (default: True)
    """
    # Discover available source functions automatically
    discovered_sources = discover_source_candles_functions()

    if not discovered_sources:
        logger.warning("No source functions were discovered")
        return

    logger.info(f"Discovered {len(discovered_sources)} source functions: {[name for name, _ in discovered_sources]}")

    for source_name, source_handler in discovered_sources:
        try:
            # Call the handler and store data
            source_data = source_handler(currency_pair, timeframe, candles, ecc)
            if source_data is not None:
                store_in_db(currency_pair, timeframe, source_data, source_name)
                logger.info(f"Successfully stored data from {source_name}")
            else:
                logger.warning(f"No data returned from {source_name}")
        except Exception as e:
            logger.error(f"Failed to fetch data from {source_name}: {str(e)}")


if __name__ == "__main__":
    refresh_source_db_by_candles("btc-usd", "m1", 500)
