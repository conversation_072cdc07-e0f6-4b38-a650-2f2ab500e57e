from app.db.sqlite.store_in_db import store_in_db
from app.core.discover_source_functions import discover_source_range_functions
from app.logger.get_logger import log, logger


@log
def refresh_source_db_by_range(currency_pair: str, timeframe: str, from_date: str, to_date: str, candles: int, ecc: bool) -> None:
    """
    Fetches candle data from multiple external sources for a specific date range and stores it in the database.

    Automatically discovers available source functions instead of using hardcoded sources.

    Args:
        currency_pair: Trading pair (e.g., 'btc-usd')
        timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
        from_date: Start date in format 'ddmmyyyy'
        to_date: End date in format 'ddmmyyyy' or 'now'
        candles: Number of candles to fetch
        ecc: Exclude current candle flag
    """
    # Discover available range source functions automatically
    discovered_sources = discover_source_range_functions()

    if not discovered_sources:
        logger.warning("No range source functions were discovered")
        return

    logger.info(f"Discovered {len(discovered_sources)} range source functions: {[name for name, _ in discovered_sources]}")

    for source_name, source_handler in discovered_sources:
        try:
            # Call the handler and store data
            source_data = source_handler(currency_pair, timeframe, from_date, to_date, candles, ecc)
            if source_data is not None:
                store_in_db(currency_pair, timeframe, source_data, source_name)
                logger.info(f"Successfully stored range data from {source_name}")
            else:
                logger.warning(f"No range data returned from {source_name}")
        except Exception as e:
            logger.error(f"Failed to fetch range data from {source_name}: {str(e)}")
