import os
import sys
import importlib

from typing import List, <PERSON><PERSON>, Callable

# Add the project root to the Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def discover_source_candles_functions() -> List[Tuple[str, Callable]]:
    """
    Discover all get_candles_from_{source} functions in app/sources/{source}/ directories.

    Returns:
        List of tuples containing (source_name, function_callable)
    """
    return _discover_functions("get_candles_from")


def discover_source_range_functions() -> List[Tuple[str, Callable]]:
    """
    Discover all get_range_from_{source} functions in app/sources/{source}/ directories.

    Returns:
        List of tuples containing (source_name, function_callable)
    """
    return _discover_functions("get_range_from")


def _discover_functions(function_prefix: str) -> List[Tuple[str, Callable]]:
    """
    Generic function to discover source functions based on prefix.

    Args:
        function_prefix: Either "get_candles_from" or "get_range_from"

    Returns:
        List of tuples containing (source_name, function_callable)
    """
    sources_path = os.path.join(project_root, 'app', 'sources')
    discovered_functions = []

    if not os.path.exists(sources_path):
        print(f"⚠️  Sources directory not found: {sources_path}")
        return discovered_functions

    # Iterate through each source directory
    for source_dir in os.listdir(sources_path):
        source_path = os.path.join(sources_path, source_dir)

        # Skip if not a directory or if it's __pycache__
        if not os.path.isdir(source_path) or source_dir.startswith('__'):
            continue

        # Look for {function_prefix}_{source}.py file
        expected_filename = f"{function_prefix}_{source_dir}.py"
        expected_filepath = os.path.join(source_path, expected_filename)

        if os.path.exists(expected_filepath):
            try:
                # Import the module
                module_name = f"app.sources.{source_dir}.{function_prefix}_{source_dir}"
                module = importlib.import_module(module_name)

                # Get the function
                function_name = f"{function_prefix}_{source_dir}"
                if hasattr(module, function_name):
                    function = getattr(module, function_name)
                    discovered_functions.append((source_dir, function))
                    print(f"✓ Discovered: {function_name} from {module_name}")
                else:
                    print(f"✗ Function {function_name} not found in {module_name}")

            except ImportError as e:
                print(f"✗ Failed to import {module_name}: {e}")
            except Exception as e:
                print(f"✗ Error discovering {source_dir}: {e}")

    return discovered_functions
